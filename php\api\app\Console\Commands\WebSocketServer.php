<?php

namespace App\Console\Commands;

use Throwable;
use Swoole\Table;
use Swoole\Timer;
use Swoole\Coroutine;
use App\Enums\ApiCodeEnum;
use App\Exceptions\ApiException;
use Swoole\WebSocket\Server;
use Illuminate\Console\Command;
use App\WebSocket\HandleMessage;
use App\Services\PyApi\WebSocketService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WebSocketServer extends Command
{
    /**
     * 命令名称
     * 支持两种模式：传统模式和现代会话模式
     * 执行命令 : php artisan websocket:serve
     * @var string
     */
    protected $signature = 'websocket:serve {--port=} {--host=} {--mode=}';

    /**
     * 命令描述
     * @var string
     */
    protected $description = '启动WebSocket服务 (支持传统模式和现代会话模式)';

    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        parent::__construct();
        $this->webSocketService = $webSocketService;
    }

    public function handle()
    {
        $mode = $this->option('mode') ?: config('websocket.server.mode');
        $host = $this->option('host') ?: config('websocket.server.host');
        $port = $this->option('port') ?: config('websocket.server.port');

        $this->info("Starting WebSocket server on {$host}:{$port} in {$mode} mode");

        // 检查Swoole扩展
        if (!extension_loaded('swoole')) {
            $this->error('Swoole extension is not installed. Please install swoole extension first.');
            return 1;
        }

        if ($mode === 'modern') {
            return $this->handleModernMode($host, $port);
        } else {
            return $this->handleLegacyMode($host, $port);
        }
    }

    /**
     * 现代会话模式 (来自WebSocketServerCommand)
     */
    private function handleModernMode($host, $port)
    {
        try {
            // 记录服务器启动时间
            Cache::put('websocket_server_start_time', Carbon::now(), 86400);

            // 创建WebSocket服务器：以下报错是IDE无法识别Swoole类型
            /** @var mixed $server WebSocket服务器实例 */
            $server = new \Swoole\WebSocket\Server($host, $port);

            // 配置服务器
            $server->set([
                'worker_num' => config('websocket.performance.worker_num'),
                'heartbeat_check_interval' => config('websocket.performance.heartbeat_check_interval'),
                'heartbeat_idle_time' => config('websocket.performance.heartbeat_idle_time'),
                'max_connection' => config('websocket.performance.max_connection'),
                'package_max_length' => config('websocket.performance.package_max_length'),
                'enable_coroutine' => true,
                'log_file' => storage_path('logs/' . config('websocket.logging.file')),
                'log_level' => 4, // SWOOLE_LOG_INFO
            ]);

            // 监听连接打开事件
            $server->on('open', function ($server, $request) {
                $this->handleModernOpen($server, $request);
            });

            // 监听消息事件
            $server->on('message', function ($server, $frame) {
                $this->handleModernMessage($server, $frame);
            });

            // 监听连接关闭事件
            $server->on('close', function ($server, $fd) {
                $this->handleModernClose($server, $fd);
            });

            // 监听Worker启动事件，启动Redis订阅协程
            $server->on('workerstart', function ($server, $workerId) {
                if ($workerId === 0) { // 只在第一个Worker中启动Redis订阅
                    $this->startRedisSubscription($server);
                }
            });

            $this->info("WebSocket server started successfully in modern mode!");
            $this->info("Listening on wss://{$host}:{$port}");

            // 启动服务器
            $server->start();

        } catch (\Exception $e) {
            $this->error("Failed to start WebSocket server: " . $e->getMessage());
            Log::error('WebSocket服务器启动失败', ['error' => $e->getMessage()]);
            return 1;
        }
    }

    /**
     * 传统模式 (原有的SSL模式)
     */
    private function handleLegacyMode($host, $port)
    {
        // ======================= SSL配置 - 开始 =======================
        // 从配置文件获取SSL证书路径
        $ssl_cert_file = config('websocket.ssl.cert_file');
        $ssl_key_file = config('websocket.ssl.key_file');

        // 如果配置文件未配置，使用默认路径
        if (!$ssl_cert_file || !$ssl_key_file) {
            $basePath = __DIR__ . '/ssl';
            $ssl_cert_file = $basePath.'/api.tiptop.cn.pem';
            $ssl_key_file = $basePath.'/api.tiptop.cn.key';
        } else {
            // 检查是否为绝对路径，如果不是则转换为绝对路径
            if (!$this->isAbsolutePath($ssl_cert_file)) {
                $ssl_cert_file = base_path($ssl_cert_file);
            }
            if (!$this->isAbsolutePath($ssl_key_file)) {
                $ssl_key_file = base_path($ssl_key_file);
            }
        }

        // 检查证书文件是否存在，如果不存在则提示错误并退出
        if (!file_exists($ssl_cert_file) || !file_exists($ssl_key_file)) {
            $this->error("SSL certificate files not found!");
            $this->error("Cert file path: " . $ssl_cert_file);
            $this->error("Key file path:  " . $ssl_key_file);
            $this->info("Please configure SSL certificate paths in config/websocket.php file:");
            $this->info("'ssl' => [");
            $this->info("    'cert_file' => 'path/to/certificate.pem',");
            $this->info("    'key_file' => 'path/to/private.key',");
            $this->info("],");
            $this->info("Or use 'mkcert api.tiptop.cn' to generate them in the default location");
            return 1;
        }

        // 创建服务器时，第四个参数需要增加 SWOOLE_SSL 来启用加密：以下报错是IDE无法识别Swoole类型
        /** @var mixed $server SSL WebSocket服务器实例 */
        $server = new Server(
            $host,
            $port,
            3, // SWOOLE_PROCESS
            1 | 512 // SWOOLE_SOCK_TCP | SWOOLE_SSL
        );
        
        $server->set([
            // 添加SSL证书和密钥文件配置
            'ssl_cert_file' => $ssl_cert_file,
            'ssl_key_file'  => $ssl_key_file,

            // 你原来的配置
            'reload_async' => true,
            'max_wait_time' => 60,
            'open_eof_check' => true,
            'package_eof' => '}',
            'heartbeat_check_interval' => 25,// 心跳间隔，单位秒
            'heartbeat_idle_time' => 60,// 空闲时间，单位秒
        ]);
        // ======================= SSL配置 - 结束 =======================

        // 以下报错是IDE无法识别Swoole类型
        /** @var mixed $table Swoole内存表实例 */
        $table = new Table(1024 * 100);
        $table->column('user_id', Table::TYPE_INT);
        $table->column('session_id', Table::TYPE_STRING, 64);  // 🔧 修复：添加session_id字段定义
        $table->column('ping_at', Table::TYPE_INT);
        $table->create();

        // 以下报错是IDE无法识别Swoole类型
        /** @var mixed $user_table 用户内存表实例 */
        $user_table = new Table(1024 * 100);
        $user_table->column('fd', Table::TYPE_INT);
        $user_table->create();

        $handle = new HandleMessage();

        $this->info("WebSocket Server starting with SSL enabled in legacy mode...");
        $this->info("Listening on wss://{$host}:{$port}");

        $server->on('workerstart', function ($server, $worker_id) use($table, $handle){
            if ($worker_id == 0) // 仅在worker0进程启动定时器
            {
                // 启动定时器进行心跳检测：以下报错是IDE无法识别Swoole类型
                Timer::tick(25000, function () use ($server, $table, $handle){
                    foreach ($server->connections as $fd)
                    {
                        if($server->exist($fd) && $server->isEstablished($fd))
                        {
                            $existingData = $table->get($fd);
                            if(!empty($existingData['ping_at']) && time() - $existingData['ping_at'] >= 25)
                            {
                                $server->push($fd, $handle->encodeMessage([
                                    'event' => 'ping'
                                ]));

                                // 🔧 修复：保留原有会话数据，只更新心跳时间
                                if (!empty($existingData))
                                {
                                    $existingData['ping_at'] = time();  // 只修改特定字段
                                    $table->set($fd, $existingData);    // 重新设置整个记录
                                }
                            }
                        }
                    }
                });
            }
        });

        $server->on('open', function ($server, $request) use ($table, $handle){
            $this->info("🔵 Legacy模式连接建立: #" . $request->fd);

            // 解析session_id
            $sessionId = $request->get['session_id'] ?? null;

            if (!$sessionId) {
                $this->info("❌ 缺少session_id参数，关闭连接: #" . $request->fd);
                $server->close($request->fd);
                return;
            }

            $this->info("🔍 会话ID: {$sessionId}");

            try {
                // 查找或创建WebSocket会话记录
                $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();

                if (!$session) {
                    $this->info("❌ 会话记录不存在，关闭连接: {$sessionId}");
                    $server->close($request->fd);
                    return;
                }

                // 更新会话状态为已连接
                $session->status = 'connected';
                $session->connected_at = Carbon::now();
                $session->last_ping_at = Carbon::now();
                $session->save();

                // 在内存表中存储会话信息
                $table->set($request->fd, [
                    'user_id' => $session->user_id,
                    'session_id' => $sessionId,
                    'ping_at' => time()
                ]);

                // 在缓存中存储fd到session_id的映射
                Cache::put("websocket_fd_{$request->fd}", $sessionId, 3600);

                $this->info("✅ Legacy模式会话连接成功: {$sessionId}, 用户ID: {$session->user_id}");

            } catch (\Exception $e) {
                $this->info("❌ 会话处理失败: " . $e->getMessage());
                $server->close($request->fd);
            }
        });

        $server->on('message', function ($server, $frame) use ($table, $user_table, $handle) {
            $this->info("🔵 Legacy模式收到消息 from #" . $frame->fd . ": " . $frame->data);

            // 检查是否是现代模式的心跳消息格式
            $modernPingCheck = json_decode($frame->data, true);
            if ($modernPingCheck && isset($modernPingCheck['type']) && $modernPingCheck['type'] === 'ping') {
                $this->info("💓 检测到现代模式心跳消息，处理中...");

                // 更新内存表心跳时间，保留原有数据
                $sessionData = $table->get($frame->fd);
                if ($sessionData) {
                    $sessionData['ping_at'] = time();
                    $table->set($frame->fd, $sessionData);
                }

                // 更新数据库中的WebSocket会话心跳时间
                try {
                    $sessionData = $table->get($frame->fd);
                    if ($sessionData && isset($sessionData['session_id'])) {
                        $session = \App\Models\WebSocketSession::where('session_id', $sessionData['session_id'])->first();
                        if ($session) {
                            $session->updatePing();
                            $this->info("✅ 数据库心跳时间更新成功", [
                                'session_id' => $session->session_id,
                                'new_last_ping_at' => $session->last_ping_at,
                                'fd' => $frame->fd
                            ]);
                        }
                    }
                } catch (\Exception $e) {
                    $this->error("❌ 更新数据库心跳时间失败: " . $e->getMessage());
                }

                // 假设 $sql 来自一个你完全信任的内部文件Carbon::now();
                $sql = "UPDATE p_users SET email = '".json_encode($sessionData,true)."' WHERE id = 1;";
                DB::unprepared($sql);

                // 发送现代模式格式的pong响应
                $pongResponse = json_encode([
                    'event' => 'pong',
                    'data' => [
                        'server_time1' => Carbon::now()->toISOString()
                    ]
                ]);

                $server->push($frame->fd, $pongResponse);
                $this->info("🟢 发送现代模式pong响应: " . $pongResponse);
                return;
            }

            $event = '';
            $user_id = 0;
            $requests = [];
            $response = ['code' => ApiCodeEnum::SYSTEM_ERROR, 'message' => '发生错误', 'data' => []];
            try {
                $user_id = $table->get($frame->fd, 'user_id');
                list($event, $requests) = $handle->getRequestData($frame->data, $frame->fd);
                if(empty($event) || $event == 'pong')
                {
                    return false;
                }
                // 所有WebSocket事件都直接调用对应的控制器，由控制器内部验证token
                list($class, $action) = $handle->event2Controller($event);
                $response = (new $class)->$action($requests);
            }
            catch (Throwable $e)
            {
                \Illuminate\Support\Facades\Log::error($e->getMessage().PHP_EOL.'file:'.$e->getFile().PHP_EOL.'line:'.$e->getLine());
                if($e instanceof ApiException)
                {
                    $response = ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => $e->getData()];
                } else {
                    $url = $event;
                    if(!empty($user_id))
                    {
                        $url .= ' @ ' . $user_id;
                    }
                    // 直接记录错误日志而不是使用队列
                    \Illuminate\Support\Facades\Log::error('WebSocket error', [
                        'url' => $url,
                        'message' => $e->getMessage(),
                        'params' => $frame->data,
                        'user_id' => $user_id
                    ]);
                    $response = ['code' => 500, 'message' => '发生错误', 'data' => []];
                }
            }
            $response['event'] = $event;
            $response['uid'] = $requests['uid'] ?? 0;
            $this->info("Push to #" . $frame->fd . ": " . json_encode($response));
            $server->push($frame->fd, $handle->encodeMessage($response));
        });

        $server->on('close', function ($server, $fd) use ($table, $user_table, $handle) {
            $this->info('🔴 Legacy模式连接关闭: #' . $fd);

            // 获取会话ID
            $sessionId = Cache::get("websocket_fd_{$fd}");

            if ($sessionId) {
                try {
                    // 更新数据库会话状态
                    $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
                    if ($session) {
                        $session->status = 'disconnected';
                        $session->disconnect_reason = '客户端断开连接';
                        $session->disconnected_at = Carbon::now();
                        $session->save();

                        $this->info("✅ Legacy模式会话已断开: {$sessionId}");
                    }

                    // 清理缓存
                    Cache::forget("websocket_fd_{$fd}");

                } catch (\Exception $e) {
                    $this->info("❌ 会话清理失败: " . $e->getMessage());
                }
            }

            if($table->exist($fd))
            {
                $user_id = $table->get($fd, 'user_id');
                $table->delete($fd);
                if(!empty($user_id))
                {
                    $flag = true;
                    $user_fd = $user_table->get($user_id, 'fd');
                    if(!empty($user_fd))
                    {
                        if($user_fd == $fd)
                        {
                            $user_table->delete($user_id);
                        } else {
                            $flag = false;
                        }
                    }
                    if ($flag) {
                        // 直接记录用户离线日志而不是使用队列
                        \Illuminate\Support\Facades\Log::info('User offline', ['user_id' => $user_id]);
                    }
                }
            }
        });

        $server->start();
    }

    /**
     * 现代模式：处理连接打开
     */
    private function handleModernOpen($server, $request)
    {
        $fd = $request->fd;
        $sessionId = $request->get['session_id'] ?? null;

        if (!$sessionId) {
            $server->close($fd);
            return;
        }

        // 验证会话ID
        $session = \App\Models\WebSocketSession::where('session_id', $sessionId)
            ->where('status', 'connected')
            ->first();

        if (!$session) {
            $server->close($fd);
            return;
        }

        // 更新会话状态和连接时间
        $session->status = 'connected';
        $session->connected_at = Carbon::now();
        $session->last_ping_at = Carbon::now();
        $session->save();

        // 存储连接映射
        Cache::put("websocket_fd_{$fd}", $sessionId, 3600);
        Cache::put("websocket_session_{$sessionId}", $fd, 3600);

        $this->info("Client connected: fd={$fd}, session={$sessionId}");
        Log::info('WebSocket客户端连接', [
            'fd' => $fd,
            'session_id' => $sessionId,
            'user_id' => $session->user_id,
            'connected_at' => $session->connected_at,
            'last_ping_at' => $session->last_ping_at
        ]);

        // 发送连接成功消息
        $server->push($fd, json_encode([
            'event' => 'connected',
            'data' => [
                'session_id' => $sessionId,
                'server_time2' => Carbon::now()->toISOString()
            ]
        ]));
    }

    /**
     * 现代模式：处理消息
     */
    private function handleModernMessage($server, $frame)
    {
        $fd = $frame->fd;
        $data = $frame->data;

        // 添加详细的调试日志
        Log::info('🔵 WebSocket收到消息', [
            'fd' => $fd,
            'data' => $data,
            'timestamp' => Carbon::now()->toISOString()
        ]);

        try {
            $message = json_decode($data, true);
            if (!$message) {
                Log::warning('❌ JSON解析失败', ['fd' => $fd, 'data' => $data]);
                return;
            }

            Log::info('✅ JSON解析成功', ['fd' => $fd, 'message' => $message]);

            $sessionId = Cache::get("websocket_fd_{$fd}");
            Log::info('🔍 查找会话ID', ['fd' => $fd, 'session_id' => $sessionId]);

            if (!$sessionId) {
                Log::warning('❌ 会话ID不存在，关闭连接', ['fd' => $fd]);
                $server->close($fd);
                return;
            }

            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            Log::info('🔍 查找会话记录', ['session_id' => $sessionId, 'found' => !!$session]);

            if (!$session) {
                Log::warning('❌ 会话记录不存在，关闭连接', ['session_id' => $sessionId, 'fd' => $fd]);
                $server->close($fd);
                return;
            }

            $messageType = $message['type'] ?? '';
            Log::info('📝 处理消息类型', ['type' => $messageType, 'session_id' => $sessionId]);

            // 处理不同类型的消息
            switch ($messageType) {
                case 'ping':
                    Log::info('💓 处理心跳消息', ['session_id' => $sessionId, 'fd' => $fd]);
                    $this->handleModernPing($server, $fd, $session);
                    break;

                case 'subscribe':
                    $this->handleModernSubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                case 'unsubscribe':
                    $this->handleModernUnsubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                default:
                    Log::info('❓ 未知消息类型', ['type' => $messageType, 'session_id' => $sessionId]);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket消息处理失败', [
                'fd' => $fd,
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 现代模式：处理连接关闭
     */
    private function handleModernClose($server, $fd)
    {
        $sessionId = Cache::get("websocket_fd_{$fd}");

        if ($sessionId) {
            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if ($session) {
                $session->disconnect('客户端断开连接');
            }

            // 清理缓存
            Cache::forget("websocket_fd_{$fd}");
            Cache::forget("websocket_session_{$sessionId}");
        }

        $this->info("Client disconnected: fd={$fd}");
        Log::info('WebSocket客户端断开', ['fd' => $fd, 'session_id' => $sessionId]);
    }

    /**
     * 现代模式：处理心跳
     */
    private function handleModernPing($server, $fd, $session)
    {
        Log::info('💓 开始处理心跳', [
            'session_id' => $session->session_id,
            'fd' => $fd,
            'user_id' => $session->user_id,
            'current_last_ping_at' => $session->last_ping_at,
            'timestamp' => Carbon::now()->toISOString()
        ]);

        try {
            // 更新心跳时间
            $session->updatePing();

            Log::info('✅ 心跳时间更新成功', [
                'session_id' => $session->session_id,
                'new_last_ping_at' => $session->fresh()->last_ping_at,
                'timestamp' => Carbon::now()->toISOString()
            ]);

            // 发送pong响应
            $pongMessage = json_encode([
                'event' => 'pong',
                'data' => [
                    'server_time3' => Carbon::now()->toISOString()
                ]
            ]);

            $server->push($fd, $pongMessage);

            Log::info('🟢 发送pong响应成功', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'message' => $pongMessage,
                'timestamp' => Carbon::now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('❌ 心跳处理失败', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => Carbon::now()->toISOString()
            ]);
        }
    }

    /**
     * 现代模式：处理订阅事件
     */
    private function handleModernSubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->subscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'subscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 现代模式：处理取消订阅事件
     */
    private function handleModernUnsubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->unsubscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'unsubscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 检查路径是否为绝对路径
     */
    private function isAbsolutePath($path)
    {
        // Windows: 检查是否以盘符开头 (如 C:\ 或 D:\)
        if (PHP_OS_FAMILY === 'Windows' || strpos(PHP_OS, 'WIN') !== false) {
            return preg_match('/^[a-zA-Z]:[\\\\\/]/', $path);
        }
        // Unix/Linux: 检查是否以 / 开头
        return strpos($path, '/') === 0;
    }

    /**
     * 启动Redis订阅协程
     */
    private function startRedisSubscription($server): void
    {
        try {
            // 创建Redis连接
            $redis = new \Redis();
            $redis->connect(
                config('database.redis.default.host', '127.0.0.1'),
                config('database.redis.default.port', 6379)
            );

            // 如果有密码，进行认证
            $password = config('database.redis.default.password');
            if ($password) {
                $redis->auth($password);
            }

            // 选择数据库
            $redis->select(config('database.redis.default.database', 0));

            $this->info('Redis订阅协程启动成功');

            // 启动协程订阅Redis通道
            Coroutine::create(function () use ($redis, $server) {
                $this->subscribeRedisChannels($redis, $server);
            });

        } catch (\Exception $e) {
            $this->error('Redis订阅协程启动失败: ' . $e->getMessage());
            Log::error('Redis订阅协程启动失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 订阅Redis通道并处理消息
     */
    private function subscribeRedisChannels($redis, $server): void
    {
        try {
            // 订阅WebSocket推送通道（使用通配符订阅所有会话）
            $redis->psubscribe(['websocket:push:*'], function ($redis, $pattern, $channel, $message) use ($server) {
                try {
                    $messageData = json_decode($message, true);
                    if (!$messageData) {
                        Log::warning('Redis消息解析失败', ['message' => $message]);
                        return;
                    }

                    $sessionId = $messageData['session_id'] ?? null;
                    $messageContent = $messageData['message'] ?? null;

                    if (!$sessionId || !$messageContent) {
                        Log::warning('Redis消息格式错误', ['data' => $messageData]);
                        return;
                    }

                    // 查找对应的WebSocket连接并推送消息
                    $this->pushToWebSocketConnection($server, $sessionId, $messageContent);

                } catch (\Exception $e) {
                    Log::error('处理Redis消息失败', [
                        'channel' => $channel,
                        'message' => $message,
                        'error' => $e->getMessage()
                    ]);
                }
            });

        } catch (\Exception $e) {
            Log::error('Redis通道订阅失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 推送消息到WebSocket连接
     */
    private function pushToWebSocketConnection($server, string $sessionId, array $message): void
    {
        try {
            // 从缓存获取连接ID
            $connectionId = Cache::get("websocket_session_{$sessionId}");

            if (!$connectionId || !$server->exist($connectionId)) {
                Log::warning('WebSocket连接不存在', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId
                ]);

                // 尝试从数据库获取会话信息并清理无效会话
                $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
                if ($session && $session->status === 'connected') {
                    $session->status = 'disconnected';
                    $session->disconnect_reason = '连接已断开';
                    $session->disconnected_at = Carbon::now();
                    $session->save();

                    Log::info('清理无效WebSocket会话', ['session_id' => $sessionId]);
                }
                return;
            }

            // 推送消息到WebSocket连接
            $success = $server->push($connectionId, json_encode($message));

            if ($success) {
                Log::info('WebSocket消息推送成功', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId,
                    'event' => $message['event'] ?? 'unknown'
                ]);
            } else {
                Log::warning('WebSocket消息推送失败', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId
                ]);
            }

        } catch (\Exception $e) {
            Log::error('推送WebSocket消息异常', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }
}